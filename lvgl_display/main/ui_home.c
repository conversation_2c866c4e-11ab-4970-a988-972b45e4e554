#include <math.h>
#include "esp_log.h"
#include "driver/ledc.h"
#include "driver/gpio.h"
#include "lvgl.h"
#include "st7789_driver.h"
#include "cst816t_driver.h"
#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_cali.h"
#include "esp_adc/adc_cali_scheme.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// PWM电压输出相关定义
#define PWM_GPIO_NUM GPIO_NUM_32
#define PWM_TIMER LEDC_TIMER_0
#define PWM_MODE LEDC_LOW_SPEED_MODE
#define PWM_CHANNEL LEDC_CHANNEL_0
#define PWM_DUTY_RES LEDC_TIMER_13_BIT  // 13位分辨率 (0-8191)
#define PWM_FREQUENCY 1000              // 1kHz频率
#define MAX_VOLTAGE 3.271f              // 最大输出电压改为3.271V
#define MAX_DUTY ((1 << PWM_DUTY_RES) - 1)  // 最大占空比

// 电位器控制相关定义
#define POTENTIOMETER_ADC_CHANNEL   ADC_CHANNEL_6   // GPIO34对应ADC_CHANNEL_6
#define POTENTIOMETER_ADC_ATTEN     ADC_ATTEN_DB_12 // 0-3.3V量程
#define POTENTIOMETER_ADC_BITWIDTH  ADC_BITWIDTH_12 // 12位精度
#define POTENTIOMETER_UPDATE_PERIOD 50              // 50ms更新一次

// 电流检测相关定义 (使用GPIO33)
#define CURRENT_ADC_CHANNEL   ADC_CHANNEL_5   // GPIO33对应ADC1_CHANNEL_5
#define CURRENT_ADC_UNIT      ADC_UNIT_1      // 使用ADC1单元
#define CURRENT_ADC_ATTEN     ADC_ATTEN_DB_12 // 0-3.3V量程
#define CURRENT_ADC_BITWIDTH  ADC_BITWIDTH_12 // 12位精度
#define CURRENT_UPDATE_PERIOD 100             // 100ms更新一次
#define CURRENT_SAMPLE_COUNT  32              // 增加采样次数以提高精度

// 模式切换按键定义
#define MODE_SWITCH_GPIO GPIO_NUM_0                 // BOOT按键作为模式切换
#define DEBOUNCE_TIME_MS 50                         // 按键防抖时间

// ACS712-05B参数 (使用3.3V供电)
#define ACS712_VCC 3.3f           // 模块供电电压
#define ACS712_SENSITIVITY 0.185f // 灵敏度 (V/A) - 5A版本
// 根据实际测量调整零点电压为1.6-1.65V范围
#define ACS712_QUIESCENT_VOLTAGE 1.625f // 零点电压 (实测1.6-1.65V，取中间值)

// 电流检测校准参数
#define CURRENT_FILTER_ALPHA 0.1f  // 电流滤波系数
#define DISPLAY_FILTER_ALPHA 0.3f  // 显示滤波系数

// 激光器功率控制相关定义
#define MIN_LASER_POWER 0.0f        // 最小功率(mW)
#define MAX_LASER_POWER 41.88f      // 最大功率(mW) - 对应3.3V

// 激光器电压-功率特性参数（根据实际数据重新校准）
#define LASER_THRESHOLD_V 1.5f      // 阈值电压
#define LASER_LINEAR_START_V 1.7f   // 线性区起始电压
#define LASER_MIN_POWER 0.0493f     // 最小功率
#define LASER_QUAD_A -1.885f        // 二次项系数
#define LASER_QUAD_B 15.54f         // 一次项系数
#define LASER_QUAD_C 0.12f          // 常数项
#define LASER_LINEAR_SLOPE 22.333f  // 线性区斜率
#define LASER_LINEAR_INTERCEPT 3.228f // 线性区截距

// 校准系数（根据实际测量调整）
#define VOLTAGE_CALIBRATION_OFFSET 0.0f    // 电压校准偏移
#define VOLTAGE_CALIBRATION_GAIN 1.0f      // 电压校准增益
#define POWER_CALIBRATION_OFFSET 0.0f      // 功率校准偏移
#define POWER_CALIBRATION_GAIN 1.0f        // 功率校准增益

// 控制模式定义
typedef enum {
    CONTROL_MODE_POTENTIOMETER = 0, // 电位器控制模式
    CONTROL_MODE_POWER = 1          // 功率控制模式
} control_mode_t;

// 电位器控制参数
#define POT_VOLTAGE_THRESHOLD 0.05f     // 电位器变化阈值
#define POT_FILTER_ALPHA 0.1f           // 电位器滤波系数
#define POT_SAMPLE_COUNT 5              // 电位器采样次数

// 静态变量定义
static lv_obj_t* s_voltage_label;
static lv_obj_t* s_keyboard;
static lv_timer_t* s_backlight_timer = NULL;
static lv_timer_t* s_potentiometer_timer = NULL;
static lv_timer_t* s_current_timer = NULL;
static adc_oneshot_unit_handle_t s_adc1_handle = NULL;    // ADC1用于电位器和电流检测
static adc_cali_handle_t s_adc1_cali_handle = NULL;
static bool s_adc1_calibrated = false;

// 控制模式相关变量
static control_mode_t s_control_mode = CONTROL_MODE_POTENTIOMETER;
static float s_filtered_pot_voltage = 0.0f;        // 滤波后的电位器电压
static float s_current_output_voltage = 0.0f;      // 当前输出电压
static bool s_mode_switch_pressed = false;         // 按键状态
static uint32_t s_last_button_time = 0;            // 上次按键时间

// 模式显示相关
static lv_obj_t* s_mode_label;
static lv_obj_t* s_pot_voltage_label;

// 功率控制相关变量
static lv_obj_t* s_power_slider;    // 功率滑块
static lv_obj_t* s_power_label;     // 功率显示标签
static lv_obj_t* s_power_input;     // 功率输入框
static float s_target_power = 0.0f; // 目标功率值

// 电流检测相关变量
static lv_obj_t* s_current_label;   // 电流显示标签
static lv_obj_t* s_voltage_debug_label; // 电压调试显示标签
static float s_current_value = 0.0f; // 当前电流值(mA)

// 函数声明
void pwm_init(void);
void set_output_voltage(float voltage_v);
void backlight_keep_timer_cb(lv_timer_t *t);
void pwm_deinit(void);
void adc_init(void);
void potentiometer_timer_cb(lv_timer_t *t);
void current_timer_cb(lv_timer_t *t);
void adc_deinit(void);
void mode_switch_init(void);
void mode_switch_isr_handler(void* arg);
void switch_control_mode(void);
float read_potentiometer_voltage(void);
float read_current_enhanced(void);
void update_ui_for_mode(void);
void disable_power_controls(void);
void enable_power_controls(void);
float calculate_power_from_voltage(float voltage);
float calculate_voltage_from_power(float power);
void power_slider_event_cb(lv_event_t * e);
void power_input_event_cb(lv_event_t * e);

// 函数实现
void pwm_init(void)
{
    ledc_timer_config_t ledc_timer = {
        .duty_resolution = PWM_DUTY_RES,
        .freq_hz = PWM_FREQUENCY,
        .speed_mode = PWM_MODE,
        .timer_num = PWM_TIMER,
        .clk_cfg = LEDC_AUTO_CLK,
    };
    ESP_ERROR_CHECK(ledc_timer_config(&ledc_timer));

    ledc_channel_config_t ledc_channel = {
        .channel = PWM_CHANNEL,
        .duty = 0,
        .gpio_num = PWM_GPIO_NUM,
        .speed_mode = PWM_MODE,
        .hpoint = 0,
        .timer_sel = PWM_TIMER
    };
    ESP_ERROR_CHECK(ledc_channel_config(&ledc_channel));
    
    ESP_LOGI("PWM", "PWM initialized on GPIO%d", PWM_GPIO_NUM);
}

void set_output_voltage(float voltage_v)
{
    // 应用电压校准
    voltage_v = voltage_v * VOLTAGE_CALIBRATION_GAIN + VOLTAGE_CALIBRATION_OFFSET;
    
    if (voltage_v < 0) voltage_v = 0;
    if (voltage_v > MAX_VOLTAGE) voltage_v = MAX_VOLTAGE;
    
    // 计算占空比
    uint32_t duty = (uint32_t)((voltage_v / MAX_VOLTAGE) * MAX_DUTY);
    
    ESP_ERROR_CHECK(ledc_set_duty(PWM_MODE, PWM_CHANNEL, duty));
    ESP_ERROR_CHECK(ledc_update_duty(PWM_MODE, PWM_CHANNEL));
    
    ESP_LOGI("PWM", "Set voltage: %.3fV (duty: %lu)", voltage_v, duty);
}

// 根据电压计算功率（应用校准）
float calculate_power_from_voltage(float voltage) {
    float power;
    
    if (voltage < LASER_THRESHOLD_V) {
        power = LASER_MIN_POWER;
    } else if (voltage < LASER_LINEAR_START_V) {
        float delta_u = voltage - LASER_THRESHOLD_V;
        power = LASER_QUAD_A * delta_u * delta_u + LASER_QUAD_B * delta_u + LASER_QUAD_C;
    } else {
        float delta_u = voltage - LASER_LINEAR_START_V;
        power = LASER_LINEAR_SLOPE * delta_u + LASER_LINEAR_INTERCEPT;
    }
    
    // 应用功率校准
    return power * POWER_CALIBRATION_GAIN + POWER_CALIBRATION_OFFSET;
}

// 根据功率计算所需电压（考虑校准）
float calculate_voltage_from_power(float power) {
    // 应用功率校准反向计算
    power = (power - POWER_CALIBRATION_OFFSET) / POWER_CALIBRATION_GAIN;
    
    if (power <= LASER_MIN_POWER) {
        return 0.0f;
    } else if (power < 3.228f) {
        // 二次函数区间
        float a = LASER_QUAD_A;
        float b = LASER_QUAD_B;
        float c = LASER_QUAD_C - power;
        
        // 计算判别式
        float discriminant = b*b - 4*a*c;
        
        if (discriminant < 0) {
            return LASER_THRESHOLD_V;
        }
        
        // 计算两个解
        float x1 = (-b + sqrtf(discriminant)) / (2*a);
        float x2 = (-b - sqrtf(discriminant)) / (2*a);
        
        // 选择在合理范围内的解 (0 <= x <= 0.2)
        float x = (x1 >= 0 && x1 <= 0.2f) ? x1 : x2;
        x = (x >= 0 && x <= 0.2f) ? x : 0.0f;
        
        return LASER_THRESHOLD_V + x;
    } else {
        // 线性区间
        float voltage = LASER_LINEAR_START_V + (power - LASER_LINEAR_INTERCEPT) / LASER_LINEAR_SLOPE;
        
        // 应用电压校准反向计算
        return (voltage - VOLTAGE_CALIBRATION_OFFSET) / VOLTAGE_CALIBRATION_GAIN;
    }
}

// 功率滑块事件回调
void power_slider_event_cb(lv_event_t * e) {
    // 只在功率控制模式下响应
    if (s_control_mode != CONTROL_MODE_POWER) return;
    
    lv_obj_t * slider = lv_event_get_target(e);
    int32_t value = lv_slider_get_value(slider);
    
    float power = (float)value / 100.0f;  // 滑块值转换为功率(0-41.88mW)
    s_target_power = power;
    
    // 计算所需电压
    float voltage = calculate_voltage_from_power(s_target_power);
    s_current_output_voltage = voltage;
    set_output_voltage(s_current_output_voltage);
    
    // 更新显示标签和输入框
    char power_str[16];
    snprintf(power_str, sizeof(power_str), "%.2fmW", power);
    lv_label_set_text(s_power_label, power_str);
    
    char input_str[16];
    snprintf(input_str, sizeof(input_str), "%.2f", power);
    lv_textarea_set_text(s_power_input, input_str);
    
    // 更新电压显示
    char voltage_str[16];
    snprintf(voltage_str, sizeof(voltage_str), "%.3fV", voltage);
    lv_label_set_text(s_voltage_label, voltage_str);
}

// 功率输入框事件回调
void power_input_event_cb(lv_event_t * e) {
    // 只在功率控制模式下响应
    if (s_control_mode != CONTROL_MODE_POWER) return;
    
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t * ta = lv_event_get_target(e);
    
    if(code == LV_EVENT_FOCUSED) {
        lv_keyboard_set_textarea(s_keyboard, ta);
        lv_obj_clear_flag(s_keyboard, LV_OBJ_FLAG_HIDDEN);
    }
    else if(code == LV_EVENT_DEFOCUSED) {
        lv_keyboard_set_textarea(s_keyboard, NULL);
        lv_obj_add_flag(s_keyboard, LV_OBJ_FLAG_HIDDEN);
    }
    else if(code == LV_EVENT_VALUE_CHANGED) {
        const char* txt = lv_textarea_get_text(ta);
        float power = atof(txt);
        
        // 限制功率范围0-41.88mW
        if(power < MIN_LASER_POWER) power = MIN_LASER_POWER;
        if(power > MAX_LASER_POWER) power = MAX_LASER_POWER;
        
        // 限制精度到0.01mW
        power = roundf(power * 100.0f) / 100.0f;
        
        s_target_power = power;
        
        // 计算所需电压
        float voltage = calculate_voltage_from_power(s_target_power);
        s_current_output_voltage = voltage;
        set_output_voltage(s_current_output_voltage);
        
        // 更新滑块和标签
        int32_t slider_value = (int32_t)(power * 100);
        lv_slider_set_value(s_power_slider, slider_value, LV_ANIM_OFF);
        
        char power_str[16];
        snprintf(power_str, sizeof(power_str), "%.2fmW", power);
        lv_label_set_text(s_power_label, power_str);
        
        // 更新电压显示
        char voltage_str[16];
        snprintf(voltage_str, sizeof(voltage_str), "%.3fV", voltage);
        lv_label_set_text(s_voltage_label, voltage_str);
    }
}

void backlight_keep_timer_cb(lv_timer_t *t)
{
    // 保持背光开启的定时器回调
    ESP_LOGD("UI_HOME", "Backlight keep timer callback");
}

void pwm_deinit(void)
{
    ledc_stop(PWM_MODE, PWM_CHANNEL, 0);
    ESP_LOGI("PWM", "PWM deinitialized");
}

void adc_init(void)
{
    // 配置GPIO34为输入（电位器）
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = (1ULL << GPIO_NUM_34),
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_DISABLE,
    };
    gpio_config(&io_conf);
    
    // 配置GPIO33为输入（电流检测）
    io_conf.pin_bit_mask = (1ULL << GPIO_NUM_33);
    gpio_config(&io_conf);
    
    // 配置ADC1（电位器和电流检测）
    adc_oneshot_unit_init_cfg_t init_config1 = {
        .unit_id = ADC_UNIT_1,
    };
    ESP_ERROR_CHECK(adc_oneshot_new_unit(&init_config1, &s_adc1_handle));

    // 配置通道
    adc_oneshot_chan_cfg_t config = {
        .bitwidth = POTENTIOMETER_ADC_BITWIDTH,
        .atten = POTENTIOMETER_ADC_ATTEN,
    };
    
    // 配置电位器通道 (ADC1_CHANNEL_6)
    ESP_ERROR_CHECK(adc_oneshot_config_channel(s_adc1_handle, POTENTIOMETER_ADC_CHANNEL, &config));
    
    // 配置电流检测通道 (ADC1_CHANNEL_5)
    ESP_ERROR_CHECK(adc_oneshot_config_channel(s_adc1_handle, CURRENT_ADC_CHANNEL, &config));

    // ADC1校准（电位器和电流检测）
    adc_cali_line_fitting_config_t cali_config1 = {
        .unit_id = ADC_UNIT_1,
        .atten = POTENTIOMETER_ADC_ATTEN,
        .bitwidth = POTENTIOMETER_ADC_BITWIDTH,
    };
    esp_err_t ret1 = adc_cali_create_scheme_line_fitting(&cali_config1, &s_adc1_cali_handle);
    if (ret1 == ESP_OK) {
        s_adc1_calibrated = true;
        ESP_LOGI("ADC", "ADC1 calibration enabled");
    } else {
        ESP_LOGW("ADC", "ADC1 calibration failed, using raw values");
        s_adc1_calibrated = false;
    }
    
    ESP_LOGI("ADC", "ADC initialized on GPIO34 (potentiometer) and GPIO33 (current)");
}

float read_potentiometer_voltage(void)
{
    int adc_raw_sum = 0;
    
    // 多次采样平均
    for(int i = 0; i < POT_SAMPLE_COUNT; i++) {
        int adc_raw = 0;
        ESP_ERROR_CHECK(adc_oneshot_read(s_adc1_handle, POTENTIOMETER_ADC_CHANNEL, &adc_raw));
        adc_raw_sum += adc_raw;
        vTaskDelay(pdMS_TO_TICKS(1));
    }
    
    int adc_raw_avg = adc_raw_sum / POT_SAMPLE_COUNT;
    
    // 转换为电压
    int voltage_mv = 0;
    if (s_adc1_calibrated) {
        ESP_ERROR_CHECK(adc_cali_raw_to_voltage(s_adc1_cali_handle, adc_raw_avg, &voltage_mv));
    } else {
        voltage_mv = (adc_raw_avg * 3300) / 4095;
    }
    
    return voltage_mv / 1000.0f;
}

// 增强的电流读取函数
float read_current_enhanced(void)
{
    int adc_readings[CURRENT_SAMPLE_COUNT];
    
    // 采集多个样本
    for(int i = 0; i < CURRENT_SAMPLE_COUNT; i++) {
        ESP_ERROR_CHECK(adc_oneshot_read(s_adc1_handle, CURRENT_ADC_CHANNEL, &adc_readings[i]));
        vTaskDelay(pdMS_TO_TICKS(2)); // 增加采样间隔
    }
    
    // 排序并去除异常值（中值滤波）
    for(int i = 0; i < CURRENT_SAMPLE_COUNT-1; i++) {
        for(int j = i+1; j < CURRENT_SAMPLE_COUNT; j++) {
            if(adc_readings[j] < adc_readings[i]) {
                int temp = adc_readings[i];
                adc_readings[i] = adc_readings[j];
                adc_readings[j] = temp;
            }
        }
    }
    
    // 去除最高和最低的20%值，取中间60%的平均值
    int discard_count = CURRENT_SAMPLE_COUNT / 5;
    int filtered_sum = 0;
    for(int i = discard_count; i < CURRENT_SAMPLE_COUNT - discard_count; i++) {
        filtered_sum += adc_readings[i];
    }
    
    int adc_raw_avg = filtered_sum / (CURRENT_SAMPLE_COUNT - 2*discard_count);
    
    // 转换为电压
    float voltage;
    if (s_adc1_calibrated) {
        int voltage_mv;
        ESP_ERROR_CHECK(adc_cali_raw_to_voltage(s_adc1_cali_handle, adc_raw_avg, &voltage_mv));
        voltage = voltage_mv / 1000.0f;
    } else {
        voltage = (adc_raw_avg * 3.3f) / 4095.0f;
    }
    
    // 计算电流 (ACS712公式: I = (Vout - Vquiescent) / Sensitivity)
    float current_amperes = (voltage - ACS712_QUIESCENT_VOLTAGE) / ACS712_SENSITIVITY;
    
    // 转换为毫安
    float current_ma = current_amperes * 1000.0f;
    
    // 应用额外的软件滤波
    static float filtered_current = 0;
    
    filtered_current = filtered_current * (1.0f - CURRENT_FILTER_ALPHA) + 
                      current_ma * CURRENT_FILTER_ALPHA;
    
    // 确保电流不为负（只检测零点电压以上的电流）
    if (filtered_current < 0) filtered_current = 0;
    
    return filtered_current;
}

void potentiometer_timer_cb(lv_timer_t *t)
{
    // 检查模式切换按键
    if (s_mode_switch_pressed) {
        s_mode_switch_pressed = false;
        switch_control_mode();
    }
    
    // 读取电位器电压
    float pot_voltage = read_potentiometer_voltage();
    
    // 滤波处理
    s_filtered_pot_voltage = s_filtered_pot_voltage * (1.0f - POT_FILTER_ALPHA) + 
                            pot_voltage * POT_FILTER_ALPHA;
    
    // 更新电位器电压显示
    char pot_str[32];
    snprintf(pot_str, sizeof(pot_str), "Input: %.3fV", s_filtered_pot_voltage);
    lv_label_set_text(s_pot_voltage_label, pot_str);
    
    // 根据控制模式处理
    if (s_control_mode == CONTROL_MODE_POTENTIOMETER) {
        // 电位器控制模式：将输入电压映射到0-3.271V输出
        float output_voltage = (s_filtered_pot_voltage / 3.3f) * MAX_VOLTAGE;
        
        // 检查电压变化是否超过阈值
        if (fabsf(output_voltage - s_current_output_voltage) > POT_VOLTAGE_THRESHOLD) {
            s_current_output_voltage = output_voltage;
            set_output_voltage(s_current_output_voltage);
            
            // 更新显示
            char voltage_str[16];
            snprintf(voltage_str, sizeof(voltage_str), "%.3fV", s_current_output_voltage);
            lv_label_set_text(s_voltage_label, voltage_str);
            
            // 在电位器模式下也显示当前功率
            float current_power = calculate_power_from_voltage(s_current_output_voltage);
            char power_str[16];
            snprintf(power_str, sizeof(power_str), "%.2fmW", current_power);
            lv_label_set_text(s_power_label, power_str);
        }
    } else if (s_control_mode == CONTROL_MODE_POWER) {
        // 功率控制模式下，显示当前功率
        float current_power = calculate_power_from_voltage(s_current_output_voltage);
        char power_str[16];
        snprintf(power_str, sizeof(power_str), "%.2fmW", current_power);
        lv_label_set_text(s_power_label, power_str);
    }
}

void current_timer_cb(lv_timer_t *t)
{
    // 读取电流值（毫安）
    float current_ma = read_current_enhanced();
    s_current_value = current_ma;
    
    // 创建平滑的显示值
    static float displayed_current = 0;
    
    displayed_current = displayed_current * (1.0f - DISPLAY_FILTER_ALPHA) + 
                       current_ma * DISPLAY_FILTER_ALPHA;
    
    // 更新电流显示
    char current_str[16];
    if (displayed_current < 10.0) {
        snprintf(current_str, sizeof(current_str), "%.2fmA", displayed_current);
    } else if (displayed_current < 100.0) {
        snprintf(current_str, sizeof(current_str), "%.1fmA", displayed_current);
    } else {
        snprintf(current_str, sizeof(current_str), "%.0fmA", displayed_current);
    }
    lv_label_set_text(s_current_label, current_str);
    
    // 显示ACS712输出电压值用于调试
    float measured_voltage = (s_current_value * ACS712_SENSITIVITY / 1000.0f) + ACS712_QUIESCENT_VOLTAGE;
    char voltage_str[16];
    snprintf(voltage_str, sizeof(voltage_str), "VO:%.3fV", measured_voltage);
    lv_label_set_text(s_voltage_debug_label, voltage_str);
}

void adc_deinit(void)
{
    if (s_adc1_calibrated && s_adc1_cali_handle) {
        adc_cali_delete_scheme_line_fitting(s_adc1_cali_handle);
        s_adc1_calibrated = false;
    }
    if (s_adc1_handle) {
        adc_oneshot_del_unit(s_adc1_handle);
    }
    
    // 移除中断处理
    gpio_isr_handler_remove(MODE_SWITCH_GPIO);
    gpio_uninstall_isr_service();
    
    ESP_LOGI("ADC", "ADC deinitialized");
}

void mode_switch_init(void)
{
    // 配置模式切换按键
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_NEGEDGE,
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = (1ULL << MODE_SWITCH_GPIO),
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_ENABLE,
    };
    gpio_config(&io_conf);
    
    // 安装中断服务
    gpio_install_isr_service(0);
    gpio_isr_handler_add(MODE_SWITCH_GPIO, mode_switch_isr_handler, NULL);
    
    ESP_LOGI("MODE_SWITCH", "Mode switch button initialized on GPIO%d", MODE_SWITCH_GPIO);
}

void IRAM_ATTR mode_switch_isr_handler(void* arg)
{
    uint32_t current_time = xTaskGetTickCountFromISR() * portTICK_PERIOD_MS;
    
    // 防抖处理
    if (current_time - s_last_button_time > DEBOUNCE_TIME_MS) {
        s_mode_switch_pressed = true;
        s_last_button_time = current_time;
    }
}

void switch_control_mode(void)
{
    if (s_control_mode == CONTROL_MODE_POTENTIOMETER) {
        s_control_mode = CONTROL_MODE_POWER;
        enable_power_controls();
        ESP_LOGI("MODE_SWITCH", "Switched to POWER mode");
    } else {
        s_control_mode = CONTROL_MODE_POTENTIOMETER;
        disable_power_controls();
        ESP_LOGI("MODE_SWITCH", "Switched to POTENTIOMETER mode");
    }
    
    update_ui_for_mode();
}

void update_ui_for_mode(void)
{
    if (s_control_mode == CONTROL_MODE_POTENTIOMETER) {
        lv_label_set_text(s_mode_label, "Mode: Potentiometer Control");
        lv_obj_set_style_text_color(s_mode_label, lv_color_hex(0xFF8000), 0);
    } else {
        lv_label_set_text(s_mode_label, "Mode: Power Control");
        lv_obj_set_style_text_color(s_mode_label, lv_color_hex(0x0080FF), 0);
    }
}

void disable_power_controls(void)
{
    // 隐藏功率输入控件（但保留显示）
    lv_obj_add_flag(s_power_input, LV_OBJ_FLAG_HIDDEN);
}

void enable_power_controls(void)
{
    // 显示功率输入控件
    lv_obj_clear_flag(s_power_input, LV_OBJ_FLAG_HIDDEN);
}

void ui_home_create(void)
{
    pwm_init();
    adc_init();
    mode_switch_init();
    
    lv_obj_set_style_bg_color(lv_scr_act(), lv_color_black(), 0);
    
    // 创建标题
    lv_obj_t* title = lv_label_create(lv_scr_act());
    lv_obj_set_pos(title, 20, 20);
    lv_obj_set_style_text_font(title, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_color(title, lv_color_white(), 0);
    lv_label_set_text(title, "Laser Power Control (0-3.271V)");
    
    // 模式显示
    s_mode_label = lv_label_create(lv_scr_act());
    lv_obj_set_pos(s_mode_label, 20, 45);
    lv_obj_set_style_text_font(s_mode_label, &lv_font_montserrat_14, 0);
    lv_label_set_text(s_mode_label, "Mode: Potentiometer Control");
    
    // 电位器输入电压显示
    s_pot_voltage_label = lv_label_create(lv_scr_act());
    lv_obj_set_pos(s_pot_voltage_label, 20, 65);
    lv_obj_set_style_text_font(s_pot_voltage_label, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_color(s_pot_voltage_label, lv_color_hex(0x8080FF), 0);
    lv_label_set_text(s_pot_voltage_label, "Input: 0.000V");
    
    // 输出电压显示标签
    s_voltage_label = lv_label_create(lv_scr_act());
    lv_obj_set_pos(s_voltage_label, 20, 90);
    lv_obj_set_style_text_font(s_voltage_label, &lv_font_montserrat_20, 0);
    lv_obj_set_style_text_color(s_voltage_label, lv_color_hex(0x00FF00), 0);
    lv_label_set_text(s_voltage_label, "0.000V");
    
    // 电流显示标签
    s_current_label = lv_label_create(lv_scr_act());
    lv_obj_set_pos(s_current_label, 20, 120);
    lv_obj_set_style_text_font(s_current_label, &lv_font_montserrat_20, 0);
    lv_obj_set_style_text_color(s_current_label, lv_color_hex(0xFF0000), 0);
    lv_label_set_text(s_current_label, "0.00mA");
    
    // 电压调试显示标签
    s_voltage_debug_label = lv_label_create(lv_scr_act());
    lv_obj_set_pos(s_voltage_debug_label, 20, 145);
    lv_obj_set_style_text_font(s_voltage_debug_label, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_color(s_voltage_debug_label, lv_color_hex(0xAAAAAA), 0);
    lv_label_set_text(s_voltage_debug_label, "VO:0.000V");
    
    // 功率控制滑块
    s_power_slider = lv_slider_create(lv_scr_act());
    lv_obj_set_size(s_power_slider, 200, 25);
    lv_obj_set_pos(s_power_slider, 20, 170);
    lv_slider_set_range(s_power_slider, 0, 4188);  // 0-41.88mW，精度0.01mW
    lv_slider_set_value(s_power_slider, 0, LV_ANIM_OFF);
    lv_obj_add_event_cb(s_power_slider, power_slider_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    
    // 滑块样式
    lv_obj_set_style_bg_color(s_power_slider, lv_color_hex(0x404040), LV_PART_MAIN);
    lv_obj_set_style_bg_color(s_power_slider, lv_color_hex(0x0000FF), LV_PART_INDICATOR);
    lv_obj_set_style_bg_color(s_power_slider, lv_color_white(), LV_PART_KNOB);
    
    // 功率显示标签
    s_power_label = lv_label_create(lv_scr_act());
    lv_obj_set_pos(s_power_label, 20, 200);
    lv_obj_set_style_text_font(s_power_label, &lv_font_montserrat_20, 0);
    lv_obj_set_style_text_color(s_power_label, lv_color_hex(0x0080FF), 0);
    lv_label_set_text(s_power_label, "0.00mW");
    
    // 功率输入框
    lv_obj_t* power_input_title = lv_label_create(lv_scr_act());
    lv_obj_set_pos(power_input_title, 20, 230);
    lv_obj_set_style_text_font(power_input_title, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_color(power_input_title, lv_color_white(), 0);
    lv_label_set_text(power_input_title, "Input Power (0.00-41.88mW):");
    
    s_power_input = lv_textarea_create(lv_scr_act());
    lv_obj_set_size(s_power_input, 120, 40);
    lv_obj_set_pos(s_power_input, 20, 250);
    lv_textarea_set_accepted_chars(s_power_input, "0123456789.");
    lv_textarea_set_max_length(s_power_input, 5);  // 最多5个字符 "41.88"
    lv_textarea_set_one_line(s_power_input, true);
    lv_textarea_set_text(s_power_input, "0.00");
    lv_obj_add_event_cb(s_power_input, power_input_event_cb, LV_EVENT_ALL, NULL);
    
    // 模式切换说明
    lv_obj_t* mode_info = lv_label_create(lv_scr_act());
    lv_obj_set_pos(mode_info, 20, 300);
    lv_obj_set_style_text_font(mode_info, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_color(mode_info, lv_color_hex(0xFFFF00), 0);
    lv_label_set_text(mode_info, "Press BOOT button to switch mode");
    
    // 创建数字键盘
    s_keyboard = lv_keyboard_create(lv_scr_act());
    lv_obj_set_size(s_keyboard, LV_HOR_RES, LV_VER_RES / 2);
    lv_obj_align(s_keyboard, LV_ALIGN_BOTTOM_MID, 0, 0);
    lv_keyboard_set_mode(s_keyboard, LV_KEYBOARD_MODE_NUMBER);
    lv_obj_add_flag(s_keyboard, LV_OBJ_FLAG_HIDDEN);

    // 创建定时器
    s_backlight_timer = lv_timer_create(backlight_keep_timer_cb, 1000, NULL);
    s_potentiometer_timer = lv_timer_create(potentiometer_timer_cb, POTENTIOMETER_UPDATE_PERIOD, NULL);
    s_current_timer = lv_timer_create(current_timer_cb, CURRENT_UPDATE_PERIOD, NULL);
    
    // 初始隐藏功率输入控件
    disable_power_controls();
    
    // 初始化UI模式
    update_ui_for_mode();
    
    gpio_set_level(GPIO_NUM_26, 1);
    
    ESP_LOGI("UI_HOME", "UI created with laser power and current monitoring");
    ESP_LOGI("UI_HOME", "Current detection on GPIO33 with calibrated zero voltage (1.625V)");
}

void ui_home_destroy(void)
{
    if (s_backlight_timer) {
        lv_timer_del(s_backlight_timer);
        s_backlight_timer = NULL;
    }
    
    if (s_potentiometer_timer) {
        lv_timer_del(s_potentiometer_timer);
        s_potentiometer_timer = NULL;
    }
    
    if (s_current_timer) {
        lv_timer_del(s_current_timer);
        s_current_timer = NULL;
    }
    
    pwm_deinit();
    adc_deinit();
    
    ESP_LOGI("UI_HOME", "UI destroyed and resources cleaned up");
}